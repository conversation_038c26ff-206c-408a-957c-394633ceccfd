# Backend specific
# Database files
backend/*.db
backend/*.sqlite
backend/*.sqlite3
backend/social_network.db
*.db
*.sqlite
*.sqlite3
.trae

# Upload directories
backend/uploads/
uploads/
backend/uploads/**/*

# Go specific
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
backend/main
backend/server
backend/social-network

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out

# Dependency directories
vendor/

# Go workspace file
go.work

# Frontend specific
# Dependencies
frontend/node_modules/
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
frontend/.next/
frontend/out/
.next/
out/

# Production build
frontend/build/
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# SSL certificates
*.pem
*.key
*.crt
*.cert

# Configuration files with sensitive data
config.json
secrets.json

# Qodo's file configurations
*.qodo
.qodo
