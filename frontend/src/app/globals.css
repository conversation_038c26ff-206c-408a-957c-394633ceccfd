:root {
  --background: #ffffff;
  --foreground: #171717;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

main {
  padding-top: 60px; /* Height of the navbar */
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
  main {
    padding-top: 60px; /* Keep same padding on mobile */
  }
}

/* Disable dark mode color scheme
@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}
*/
