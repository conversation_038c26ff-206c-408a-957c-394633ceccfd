'use client';

import { useState, useEffect } from 'react';
import Button from './Button';
import styles from '@/styles/NotificationFilters.module.css';

const NotificationFilters = ({ 
  currentFilters, 
  onFilterChange, 
  onSearchChange, 
  onClearFilters,
  isLoading 
}) => {
  const [searchQuery, setSearchQuery] = useState(currentFilters.search || '');
  const [searchTimeout, setSearchTimeout] = useState(null);

  // Filter options with their labels and counts
  const filterOptions = [
    { key: '', label: 'All', icon: '📋' },
    { key: 'follows', label: 'Follows', icon: '👥' },
    { key: 'groups', label: 'Groups', icon: '🏢' },
    { key: 'posts', label: 'Posts', icon: '📝' },
    { key: 'events', label: 'Events', icon: '📅' },
  ];

  // Handle search input with debouncing
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchQuery(value);

    // Clear existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set new timeout for debounced search
    const newTimeout = setTimeout(() => {
      onSearchChange(value);
    }, 500); // 500ms delay

    setSearchTimeout(newTimeout);
  };

  // Clear search when filters are cleared externally
  useEffect(() => {
    if (!currentFilters.search && searchQuery) {
      setSearchQuery('');
    }
  }, [currentFilters.search, searchQuery]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  const hasActiveFilters = currentFilters.type || currentFilters.search;

  return (
    <div className={styles.filtersContainer}>
      {/* Filter Tabs */}
      <div className={styles.filterTabs}>
        {filterOptions.map((option) => (
          <button
            key={option.key}
            className={`${styles.filterTab} ${
              currentFilters.type === option.key ? styles.active : ''
            }`}
            onClick={() => onFilterChange(option.key)}
            disabled={isLoading}
          >
            <span className={styles.filterIcon}>{option.icon}</span>
            <span className={styles.filterLabel}>{option.label}</span>
          </button>
        ))}
      </div>

      {/* Search and Actions */}
      <div className={styles.searchAndActions}>
        {/* Search Bar */}
        <div className={styles.searchContainer}>
          <div className={styles.searchInputWrapper}>
            <span className={styles.searchIcon}>🔍</span>
            <input
              type="text"
              placeholder="Search notifications..."
              value={searchQuery}
              onChange={handleSearchChange}
              className={styles.searchInput}
              disabled={isLoading}
            />
            {searchQuery && (
              <button
                className={styles.clearSearchButton}
                onClick={() => {
                  setSearchQuery('');
                  onSearchChange('');
                }}
                title="Clear search"
              >
                ×
              </button>
            )}
          </div>
        </div>

        {/* Clear Filters Button */}
        {hasActiveFilters && (
          <Button
            variant="outline"
            size="small"
            onClick={onClearFilters}
            disabled={isLoading}
            className={styles.clearFiltersButton}
          >
            Clear Filters
          </Button>
        )}
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className={styles.activeFilters}>
          <span className={styles.activeFiltersLabel}>Active filters:</span>
          {currentFilters.type && (
            <span className={styles.activeFilter}>
              Type: {filterOptions.find(opt => opt.key === currentFilters.type)?.label}
              <button
                className={styles.removeFilterButton}
                onClick={() => onFilterChange('')}
                title="Remove type filter"
              >
                ×
              </button>
            </span>
          )}
          {currentFilters.search && (
            <span className={styles.activeFilter}>
              Search: "{currentFilters.search}"
              <button
                className={styles.removeFilterButton}
                onClick={() => {
                  setSearchQuery('');
                  onSearchChange('');
                }}
                title="Remove search filter"
              >
                ×
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default NotificationFilters;
