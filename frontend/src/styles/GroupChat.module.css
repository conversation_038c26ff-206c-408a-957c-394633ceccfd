.groupChat {
  display: flex;
  flex-direction: column;
  height: 600px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chatHeader {
  background: #10b981;
  color: white;
  padding: 1rem;
  text-align: center;
}

.chatHeader h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.messagesContainer {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: #f8f9fa;
}

.loading {
  text-align: center;
  color: #666;
  padding: 2rem;
}

.noMessages {
  text-align: center;
  color: #666;
  padding: 2rem;
  font-style: italic;
}

.messagesList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 70%;
  word-wrap: break-word;
}

.ownMessage {
  align-self: flex-end;
}

.otherMessage {
  align-self: flex-start;
}

.messageHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.senderAvatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.senderName {
  font-weight: 600;
  font-size: 0.875rem;
  color: #10b981;
}

.messageTime {
  font-size: 0.75rem;
  color: #666;
  margin-left: auto;
}

.messageContent {
  background: white;
  padding: 0.75rem 1rem;
  border-radius: 18px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  font-size: 0.9rem;
  line-height: 1.4;
}

.ownMessage .messageContent {
  background: #10b981;
  color: white;
}

.messageForm {
  border-top: 1px solid #e4e6ea;
  background: white;
}

.emojiBar {
  display: flex;
  gap: 0.25rem;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #e4e6ea;
  background: #f8f9fa;
  overflow-x: auto;
}

.emojiButton {
  background: none;
  border: none;
  font-size: 1.2rem;
  padding: 0.25rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emojiButton:hover {
  background: #e4e6ea;
}

.inputContainer {
  display: flex;
  gap: 0.5rem;
  padding: 1rem;
}

.messageInput {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #e4e6ea;
  border-radius: 20px;
  font-size: 0.9rem;
  outline: none;
  transition: border-color 0.2s;
}

.messageInput:focus {
  border-color: #10b981;
}

.messageInput:disabled {
  background: #f8f9fa;
  color: #666;
}

.sendButton {
  background: #10b981;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.9rem;
}

.sendButton:hover:not(:disabled) {
  background: #177656;
}

.sendButton:disabled {
  background: #e4e6ea;
  color: #666;
  cursor: not-allowed;
}

/* Scrollbar styling */
.messagesContainer::-webkit-scrollbar {
  width: 6px;
}

.messagesContainer::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.messagesContainer::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.messagesContainer::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.emojiBar::-webkit-scrollbar {
  height: 4px;
}

.emojiBar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.emojiBar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

/* Responsive design */
@media (max-width: 768px) {
  .groupChat {
    height: 500px;
  }
  
  .message {
    max-width: 85%;
  }
  
  .emojiBar {
    padding: 0.5rem;
  }
  
  .emojiButton {
    font-size: 1rem;
    min-width: 28px;
    height: 28px;
  }
  
  .inputContainer {
    padding: 0.75rem;
  }
  
  .messageInput {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}
