.groupPageContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

.loading, .error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  font-size: 1.1rem;
  color: #65676b;
}

.groupHeader {
  background-color: white;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 1rem;
}

.coverPhoto {
  position: relative;
  width: 100%;
  height: 300px;
  background-color: #f0f2f5;
}

.defaultCover {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #10b981, #0f9467);
}

.groupInfo {
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.groupDetails {
  flex: 1;
}

.groupName {
  font-size: 2rem;
  font-weight: 700;
  color: #1c1e21;
  margin: 0 0 0.5rem;
}

.groupMeta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.privacy, .memberCount {
  font-size: 0.9rem;
  color: #65676b;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.groupDescription {
  color: #1c1e21;
  line-height: 1.5;
  margin: 0;
  max-width: 600px;
}

.groupActions {
  display: flex;
  gap: 0.75rem;
  flex-shrink: 0;
}

.tabNavigation {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  display: flex;
  margin-bottom: 1rem;
  overflow: hidden;
}

.tabButton {
  flex: 1;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  font-weight: 600;
  color: #65676b;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 3px solid transparent;
}

.tabButton:hover {
  background-color: #f0f2f5;
}

.activeTab {
  color: #10b981;
  border-bottom-color: #10b981;
  background-color: #f8f9fa;
}

.tabContent {
  padding: 1.5rem;
  height: 600px; /* Fixed height */
  overflow-y: auto; /* Enable vertical scrolling */
  overflow-x: hidden; /* Hide horizontal scrolling */
  scroll-behavior: smooth; /* Smooth scrolling */
}

/* Custom scrollbar styling */
.tabContent::-webkit-scrollbar {
  width: 8px;
}

.tabContent::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.tabContent::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.tabContent::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.restrictedContent {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  padding: 3rem 2rem;
  text-align: center;
}

.restrictedMessage h3 {
  font-size: 1.5rem;
  color: #1c1e21;
  margin: 0 0 1rem;
}

.restrictedMessage p {
  color: #65676b;
  margin: 0 0 2rem;
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .groupPageContainer {
    padding: 0;
  }

  .coverPhoto {
    height: 200px;
  }

  .groupInfo {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .groupName {
    font-size: 1.5rem;
  }

  .groupMeta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .groupActions {
    width: 100%;
    justify-content: stretch;
  }

  .groupActions button {
    flex: 1;
  }

  .tabNavigation {
    margin: 0 1rem 1rem;
  }

  .tabButton {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .restrictedContent {
    margin: 0 1rem;
    padding: 2rem 1rem;
  }

  .tabContent {
    height: 500px; /* Smaller height for mobile */
    padding: 1rem;
  }
}
