/* Guest Home Page */
.guest<PERSON>ontainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.heroSection {
  text-align: center;
  padding: 4rem 1rem;
  background-color: #f0f2f5;
  border-radius: 8px;
  margin-bottom: 3rem;
}

.heroTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #10b981;
  margin-bottom: 1rem;
}

.heroSubtitle {
  font-size: 1.25rem;
  color: #65676b;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.heroCta {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.featuresSection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.feature {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.2s, box-shadow 0.2s;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0, 0, 0, 0.15);
}

.featureIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.feature h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #10b981;
}

.feature p {
  color: #65676b;
  line-height: 1.5;
}

/* Authenticated Home Page (Feed) */
.feedContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.feedTitle {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #10b981;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #65676b;
}

.emptyFeed {
  text-align: center;
  padding: 3rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.emptyFeed h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #10b981;
}

.emptyFeed p {
  color: #65676b;
  margin-bottom: 1.5rem;
}

.feedContent {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.feedHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #dddfe2;
}

.postsContainer {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.postsPlaceholder {
  min-height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px dashed #dddfe2;
  border-radius: 8px;
  color: #65676b;
}

/* Responsive styles */
@media (max-width: 768px) {
  .heroTitle {
    font-size: 2rem;
  }

  .heroSubtitle {
    font-size: 1.1rem;
  }

  .featuresSection {
    grid-template-columns: 1fr;
  }

  .feedHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .feedTitle {
    text-align: center;
  }
}
