.dropdownContainer {
  position: absolute;
  right: 0;
  top: 100%;
  background-color: white;
  min-width: 380px;
  max-width: 400px;
  max-height: 500px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  z-index: 1000;
  border: 1px solid rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-top: 8px;
}

.dropdownHeader {
  padding: 16px 20px 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  background-color: #f8f9fa;
}

.dropdownHeader h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
}

.notificationsList {
  max-height: 400px;
  overflow-y: auto;
  padding: 0;
}

.notificationsList::-webkit-scrollbar {
  width: 6px;
}

.notificationsList::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.notificationsList::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.notificationsList::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.emptyState {
  padding: 40px 20px;
  text-align: center;
  color: #65676b;
}

.emptyState p {
  margin: 0;
  font-size: 0.9rem;
}

.notificationItem {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.notificationItem:hover {
  background-color: #f8f9fa;
}

.notificationItem:last-child {
  border-bottom: none;
}

.notificationItem.unread {
  background-color: #f0f8ff;
  border-left: 3px solid #10b981;
}

.notificationItem.unread::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background-color: #10b981;
  border-radius: 50%;
}

.notificationSender {
  flex-shrink: 0;
  margin-right: 12px;
}

.senderAvatar {
  border-radius: 50%;
  object-fit: cover;
}

.notificationContent {
  flex: 1;
  min-width: 0;
}

.notificationText {
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 4px;
}

.senderName {
  font-weight: 600;
  color: #1a1a1a;
}

.actionText {
  color: #65676b;
}

.notificationTime {
  font-size: 0.8rem;
  color: #8a8d91;
  margin-bottom: 8px;
}

.actionsContainer {
  margin-top: 8px;
}

.quickActions {
  display: flex;
  gap: 8px;
}

.dropdownFooter {
  padding: 12px 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  background-color: #f8f9fa;
  text-align: center;
}

.viewAllLink {
  color: #10b981;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.viewAllLink:hover {
  color: #059669;
  text-decoration: underline;
}

/* Responsive design */
@media (max-width: 768px) {
  .dropdownContainer {
    position: fixed;
    right: 10px;
    left: 10px;
    top: 70px;
    min-width: auto;
    max-width: none;
    margin-top: 0;
    max-height: calc(100vh - 80px);
  }

  .notificationsList {
    max-height: calc(100vh - 160px);
  }
}

@media (max-width: 480px) {
  .dropdownContainer {
    right: 5px;
    left: 5px;
  }

  .notificationItem {
    padding: 10px 12px;
  }

  .dropdownHeader {
    padding: 12px 16px 10px;
  }

  .quickActions {
    flex-direction: column;
    gap: 6px;
  }

  .quickActions button {
    width: 100%;
    padding: 8px 12px;
  }

  .notificationText {
    font-size: 0.85rem;
  }

  .notificationTime {
    font-size: 0.75rem;
  }
}
