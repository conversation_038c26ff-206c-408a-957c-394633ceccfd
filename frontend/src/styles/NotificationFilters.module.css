.filtersContainer {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e6ea;
}

/* Filter Tabs */
.filterTabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.filterTab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 2px solid #e4e6ea;
  border-radius: 8px;
  background: white;
  color: #65676b;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.filterTab:hover {
  border-color: #10b981;
  color: #10b981;
  background-color: #f0fdf4;
}

.filterTab.active {
  border-color: #10b981;
  background-color: #10b981;
  color: white;
}

.filterTab:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.filterIcon {
  font-size: 1rem;
}

.filterLabel {
  font-weight: 500;
}

/* Search and Actions */
.searchAndActions {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.searchContainer {
  flex: 1;
  min-width: 250px;
}

.searchInputWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.searchIcon {
  position: absolute;
  left: 0.75rem;
  color: #65676b;
  font-size: 1rem;
  z-index: 1;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 2px solid #e4e6ea;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.searchInput:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.clearSearchButton {
  position: absolute;
  right: 0.5rem;
  background: none;
  border: none;
  color: #65676b;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.clearSearchButton:hover {
  background-color: #f0f2f5;
  color: #e41e3f;
}

.clearFiltersButton {
  white-space: nowrap;
}

/* Active Filters */
.activeFilters {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e4e6ea;
  flex-wrap: wrap;
}

.activeFiltersLabel {
  font-size: 0.875rem;
  color: #65676b;
  font-weight: 500;
}

.activeFilter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: #e6f2ff;
  border: 1px solid #10b981;
  border-radius: 20px;
  font-size: 0.8rem;
  color: #0f766e;
  font-weight: 500;
}

.removeFilterButton {
  background: none;
  border: none;
  color: #0f766e;
  font-size: 1rem;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.removeFilterButton:hover {
  background-color: rgba(15, 118, 110, 0.1);
  color: #e41e3f;
}

/* Responsive Design */
@media (max-width: 768px) {
  .filtersContainer {
    padding: 1rem;
  }

  .filterTabs {
    gap: 0.25rem;
  }

  .filterTab {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .filterIcon {
    font-size: 0.875rem;
  }

  .searchAndActions {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .searchContainer {
    min-width: auto;
  }

  .activeFilters {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
  }
}

@media (max-width: 480px) {
  .filterTabs {
    justify-content: center;
  }

  .filterTab {
    flex: 1;
    justify-content: center;
    min-width: 0;
    padding: 0.5rem 0.25rem;
  }

  .filterLabel {
    display: none;
  }

  .filterIcon {
    font-size: 1.25rem;
  }

  .activeFilter {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
  }
}
