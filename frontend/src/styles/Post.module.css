.post {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.postHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #f0f2f5;
}

.authorInfo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
}

.authorAvatar {
  border-radius: 50%;
  object-fit: cover;
  margin-right: 0.75rem;
}

.authorAvatarPlaceholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 0.75rem;
}

.authorName {
  font-size: 0.95rem;
  font-weight: 600;
  margin: 0;
}

.postTime {
  font-size: 0.8rem;
  color: #65676b;
  margin: 0;
}

.postActions {
  position: relative;
  display: flex;
  align-items: center;
}

.dropdownToggle {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  color: #65676b;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.2s;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdownToggle:hover {
  background-color: #f0f2f5;
}

.dropdownToggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dropdownMenu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #dddfe2;
  min-width: 150px;
  z-index: 1000;
  overflow: hidden;
  animation: dropdownFadeIn 0.15s ease-out;
  transform-origin: top right;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.dropdownItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  font-size: 0.9rem;
  color: #050505;
  transition: background-color 0.2s;
}

.dropdownItem:hover {
  background-color: #f0f2f5;
}

.dropdownItem:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.dropdownItem:last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.postContent {
  padding: 1rem;
}

.postText {
  margin: 0 0 1rem;
  white-space: pre-wrap;
  word-break: break-word;
}

.postImage {
  position: relative;
  width: 100%;
  height: 300px;
  margin-top: 0.5rem;
  border-radius: 8px;
  overflow: hidden;
}

/* Style for GIF images that use regular img tag */
.postImageElement {
  border-radius: 8px;
  max-width: 100%;
  height: auto;
  display: block;
}

.postFooter {
  padding: 0 1rem 1rem;
}

.postStats {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f2f5;
  font-size: 0.85rem;
  color: #65676b;
}

.postButtons {
  display: flex;
  justify-content: space-around;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f2f5;
}

.postButton {
  background: none;
  border: none;
  padding: 0.5rem;
  font-size: 0.9rem;
  color: #65676b;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.postButton:hover {
  background-color: #f0f2f5;
}

.postButton.liked {
  color: #e41e3f;
}

.commentsSection {
  margin-top: 1rem;
}

.commentForm {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.commentInputContainer {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
}

.commentActions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.commentImageUpload {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f0f2f5;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
}

.commentImageUpload:hover {
  background-color: #e4e6eb;
}

.commentImagePreview {
  position: relative;
  margin-top: 0.5rem;
  max-width: 200px;
}

.commentPreviewImage {
  width: 100%;
  max-height: 150px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #dddfe2;
}

.removeCommentImage {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
}

.commentImageContainer {
  margin-top: 0.5rem;
}

.commentImage {
  max-width: 200px;
  max-height: 150px;
  border-radius: 8px;
  border: 1px solid #dddfe2;
}

.commentInput {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #dddfe2;
  border-radius: 20px;
  background-color: #f0f2f5;
  color: #050505; /* Explicitly set text color */
}

.commentInput:focus {
  outline: none;
  border-color: #10b981;
}

.commentsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.noComments {
  text-align: center;
  color: #65676b;
  font-size: 0.9rem;
  padding: 1rem 0;
}

.comment {
  display: flex;
  gap: 0.75rem;
}

.commentAuthor {
  text-decoration: none;
  color: inherit;
}

.commentAvatar {
  border-radius: 50%;
  object-fit: cover;
}

.commentAvatarPlaceholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.8rem;
}

.commentContent {
  flex: 1;
}

.commentBubble {
  background-color: #f0f2f5;
  border-radius: 18px;
  padding: 0.75rem 1rem;
  max-width: fit-content;
}

.commentAuthorName {
  font-size: 0.85rem;
  font-weight: 600;
  margin: 0 0 0.25rem;
}

.commentText {
  margin: 0;
  font-size: 0.9rem;
  word-break: break-word;
}

.commentMeta {
  display: flex;
  gap: 1rem;
  margin-top: 0.25rem;
  padding-left: 0.5rem;
}

.commentTime {
  font-size: 0.75rem;
  color: #65676b;
}

.deleteCommentButton {
  background: none;
  border: none;
  font-size: 0.75rem;
  color: #65676b;
  cursor: pointer;
  padding: 0;
}

.deleteCommentButton:hover {
  text-decoration: underline;
}

/* Edit form styles */
.editForm {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.editTextarea {
  width: 100%;
  min-height: 100px;
  padding: 0.75rem;
  border: 1px solid #dddfe2;
  border-radius: 8px;
  font-family: inherit;
  font-size: 0.95rem;
  resize: vertical;
  background-color: #f8f9fa;
  color: #050505;
}

.editTextarea:focus {
  outline: none;
  border-color: #10b981;
  background-color: white;
}

.editControls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.editVisibilitySelect {
  padding: 0.5rem;
  border: 1px solid #dddfe2;
  border-radius: 6px;
  background-color: white;
  color: #050505;
  font-size: 0.9rem;
}

.editVisibilitySelect:focus {
  outline: none;
  border-color: #10b981;
}

.editButtons {
  display: flex;
  gap: 0.5rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .editControls {
    flex-direction: column;
    align-items: stretch;
  }

  .editButtons {
    justify-content: flex-end;
  }

  .dropdownMenu {
    right: -10px; /* Adjust for mobile screens */
    min-width: 140px;
  }

  .dropdownItem {
    padding: 1rem;
    font-size: 1rem;
  }
}
